-- Sample data for ReWear Platform
-- Run this after setting up the schema and RLS policies
-- Note: You'll need to create actual users through Supabase Auth first

-- Sample clothing items (you'll need to replace user IDs with actual ones from auth.users)
-- These are examples - replace the UUIDs with actual user IDs from your Supabase auth.users table

INSERT INTO public.clothing_items (
    title, description, category, type, size, condition, images, tags, uploader_id, status, points_value, featured
) VALUES 
(
    'Vintage Denim Jacket',
    'Beautiful vintage denim jacket in excellent condition. Perfect for layering and has that authentic worn-in feel that looks great with everything.',
    'women',
    'unisex',
    'M',
    'like-new',
    ARRAY['https://images.pexels.com/photos/1040945/pexels-photo-1040945.jpeg'],
    ARRAY['vintage', 'denim', 'jacket', 'casual'],
    '00000000-0000-0000-0000-000000000001', -- Replace with actual user ID
    'available',
    75,
    true
),
(
    'Designer Silk Scarf',
    'Authentic designer silk scarf with beautiful floral pattern. Never worn, still has tags.',
    'accessories',
    'women',
    'One Size',
    'new',
    ARRAY['https://images.pexels.com/photos/1040945/pexels-photo-1040945.jpeg'],
    ARRAY['designer', 'silk', 'scarf', 'luxury'],
    '00000000-0000-0000-0000-000000000001', -- Replace with actual user ID
    'available',
    120,
    true
),
(
    'Classic White Sneakers',
    'Clean white leather sneakers, barely worn. Perfect for casual outfits.',
    'shoes',
    'unisex',
    '9',
    'like-new',
    ARRAY['https://images.pexels.com/photos/1040945/pexels-photo-1040945.jpeg'],
    ARRAY['sneakers', 'white', 'leather', 'casual'],
    '00000000-0000-0000-0000-000000000002', -- Replace with actual user ID
    'available',
    60,
    false
),
(
    'Wool Winter Coat',
    'Warm wool coat perfect for winter. Has been worn but still in great condition.',
    'women',
    'women',
    'L',
    'gently-used',
    ARRAY['https://images.pexels.com/photos/1040945/pexels-photo-1040945.jpeg'],
    ARRAY['wool', 'winter', 'coat', 'warm'],
    '00000000-0000-0000-0000-000000000002', -- Replace with actual user ID
    'available',
    90,
    false
),
(
    'Kids Rainbow Dress',
    'Adorable rainbow dress for kids, size 4T. My daughter outgrew it but its still beautiful.',
    'kids',
    'kids',
    '4T',
    'gently-used',
    ARRAY['https://images.pexels.com/photos/1040945/pexels-photo-1040945.jpeg'],
    ARRAY['kids', 'dress', 'rainbow', 'colorful'],
    '00000000-0000-0000-0000-000000000001', -- Replace with actual user ID
    'available',
    40,
    false
),
(
    'Business Suit Jacket',
    'Professional navy blue suit jacket, perfect for business meetings.',
    'men',
    'men',
    '42R',
    'like-new',
    ARRAY['https://images.pexels.com/photos/1040945/pexels-photo-1040945.jpeg'],
    ARRAY['business', 'suit', 'professional', 'navy'],
    '00000000-0000-0000-0000-000000000002', -- Replace with actual user ID
    'available',
    100,
    false
);

-- Instructions for setting up the database:
-- 1. Go to your Supabase project dashboard
-- 2. Navigate to the SQL Editor
-- 3. Run the schema file (supabase-schema.sql) first
-- 4. Then run the RLS policies file (supabase-rls-policies.sql)
-- 5. Finally, run this sample data file (but replace the user IDs first)
-- 
-- To get actual user IDs:
-- 1. Set up authentication first
-- 2. Create a few test users through your app
-- 3. Query the auth.users table to get their IDs
-- 4. Replace the placeholder UUIDs in this file with real ones
-- 5. Then run this sample data script

-- Example query to get user IDs:
-- SELECT id, email FROM auth.users;
