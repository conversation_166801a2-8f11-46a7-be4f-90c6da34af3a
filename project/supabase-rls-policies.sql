-- Row Level Security (RLS) Policies for ReWear Platform
-- Run this after creating the tables

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.clothing_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.swap_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chats ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_messages ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "Users can view all profiles" ON public.users
    FOR SELECT USING (true);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Clothing items policies
CREATE POLICY "Anyone can view approved items" ON public.clothing_items
    FOR SELECT USING (status = 'available' OR status = 'in-swap' OR status = 'redeemed');

CREATE POLICY "Users can view own items" ON public.clothing_items
    FOR SELECT USING (auth.uid() = uploader_id);

CREATE POLICY "Admins can view all items" ON public.clothing_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Users can insert own items" ON public.clothing_items
    FOR INSERT WITH CHECK (auth.uid() = uploader_id);

CREATE POLICY "Users can update own items" ON public.clothing_items
    FOR UPDATE USING (auth.uid() = uploader_id);

CREATE POLICY "Admins can update any item" ON public.clothing_items
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can delete any item" ON public.clothing_items
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Swap requests policies
CREATE POLICY "Users can view own swap requests" ON public.swap_requests
    FOR SELECT USING (auth.uid() = requester_id OR auth.uid() = owner_id);

CREATE POLICY "Users can create swap requests" ON public.swap_requests
    FOR INSERT WITH CHECK (auth.uid() = requester_id);

CREATE POLICY "Item owners can update swap requests" ON public.swap_requests
    FOR UPDATE USING (auth.uid() = owner_id);

-- Chats policies
CREATE POLICY "Users can view own chats" ON public.chats
    FOR SELECT USING (auth.uid() = seller_id OR auth.uid() = buyer_id);

CREATE POLICY "Users can create chats" ON public.chats
    FOR INSERT WITH CHECK (auth.uid() = seller_id OR auth.uid() = buyer_id);

-- Chat messages policies
CREATE POLICY "Users can view messages in their chats" ON public.chat_messages
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.chats 
            WHERE id = chat_id 
            AND (seller_id = auth.uid() OR buyer_id = auth.uid())
        )
    );

CREATE POLICY "Users can send messages in their chats" ON public.chat_messages
    FOR INSERT WITH CHECK (
        auth.uid() = sender_id AND
        EXISTS (
            SELECT 1 FROM public.chats 
            WHERE id = chat_id 
            AND (seller_id = auth.uid() OR buyer_id = auth.uid())
        )
    );

-- Create functions for common operations
CREATE OR REPLACE FUNCTION get_user_points(user_id UUID)
RETURNS INTEGER AS $$
BEGIN
    RETURN (SELECT points FROM public.users WHERE id = user_id);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION update_user_points(user_id UUID, point_change INTEGER)
RETURNS VOID AS $$
BEGIN
    UPDATE public.users 
    SET points = points + point_change 
    WHERE id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to handle user creation from auth trigger
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, name, role)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'name', NEW.email),
        'user'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create user profile
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
